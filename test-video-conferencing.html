<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Conferencing Test</title>
    <link rel="stylesheet" href="video-ui-components.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1f2937;
            color: white;
            font-family: Arial, sans-serif;
        }
        .test-controls {
            margin-bottom: 20px;
            padding: 20px;
            background: #374151;
            border-radius: 8px;
        }
        .test-button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #4f46e5;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            background: #111827;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Video Conferencing System Test</h1>
    
    <div class="test-controls">
        <h2>Test Controls</h2>
        <button class="test-button" onclick="testVideoConferencing()">Start Video Call</button>
        <button class="test-button" onclick="testScreenShare()">Test Screen Share</button>
        <button class="test-button" onclick="testRecording()">Test Recording</button>
        <button class="test-button" onclick="endCall()">End Call</button>
        <button class="test-button" onclick="showStats()">Show Connection Stats</button>
        
        <div class="status" id="status">
            Ready to test video conferencing...
        </div>
    </div>

    <script src="video-conferencing.js"></script>
    <script>
        let videoConferencing = null;
        let mockSocket = null;

        // Create a mock WebSocket for testing
        function createMockSocket() {
            return {
                send: function(data) {
                    console.log('Mock socket sending:', JSON.parse(data));
                    updateStatus('Sent: ' + data);
                },
                readyState: 1 // OPEN
            };
        }

        function updateStatus(message) {
            const status = document.getElementById('status');
            status.innerHTML += '<br>' + new Date().toLocaleTimeString() + ': ' + message;
            status.scrollTop = status.scrollHeight;
        }

        function testVideoConferencing() {
            try {
                if (!videoConferencing) {
                    videoConferencing = new VideoConferencing();
                    mockSocket = createMockSocket();
                    
                    // Initialize with mock data
                    videoConferencing.initialize(mockSocket, 'test-room-123', 'user-123', 'Test User');
                    
                    // Set up callbacks
                    videoConferencing.onCallStarted = () => updateStatus('Call started successfully');
                    videoConferencing.onCallEnded = () => updateStatus('Call ended');
                    videoConferencing.onError = (error) => updateStatus('Error: ' + error);
                    
                    updateStatus('Video conferencing initialized');
                }
                
                videoConferencing.startCall();
                updateStatus('Starting video call...');
                
            } catch (error) {
                updateStatus('Error starting call: ' + error.message);
                console.error('Error:', error);
            }
        }

        function testScreenShare() {
            if (videoConferencing) {
                videoConferencing.toggleScreenShare();
                updateStatus('Toggling screen share...');
            } else {
                updateStatus('Please start a video call first');
            }
        }

        function testRecording() {
            if (videoConferencing) {
                videoConferencing.toggleRecording();
                updateStatus('Toggling recording...');
            } else {
                updateStatus('Please start a video call first');
            }
        }

        function endCall() {
            if (videoConferencing) {
                videoConferencing.endCall();
                updateStatus('Ending call...');
            } else {
                updateStatus('No active call to end');
            }
        }

        async function showStats() {
            if (videoConferencing) {
                try {
                    const stats = await videoConferencing.getConnectionStats();
                    updateStatus('Connection stats: ' + JSON.stringify(stats, null, 2));
                } catch (error) {
                    updateStatus('Error getting stats: ' + error.message);
                }
            } else {
                updateStatus('No active call for stats');
            }
        }

        // Test browser compatibility
        function checkBrowserSupport() {
            const features = {
                'getUserMedia': !!navigator.mediaDevices?.getUserMedia,
                'getDisplayMedia': !!navigator.mediaDevices?.getDisplayMedia,
                'RTCPeerConnection': !!window.RTCPeerConnection,
                'MediaRecorder': !!window.MediaRecorder,
                'WebSocket': !!window.WebSocket
            };
            
            updateStatus('Browser support check:');
            for (const [feature, supported] of Object.entries(features)) {
                updateStatus(`${feature}: ${supported ? '✅' : '❌'}`);
            }
        }

        // Run browser support check on load
        window.addEventListener('load', checkBrowserSupport);
    </script>
</body>
</html>
