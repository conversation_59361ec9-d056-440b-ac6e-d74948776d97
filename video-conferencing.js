/**
 * Advanced Video Conferencing Module
 * Google Meet-style video conferencing with WebRTC
 */

class VideoConferencing {
    constructor() {
        this.localStream = null;
        this.peerConnections = new Map(); // userId -> RTCPeerConnection
        this.remoteStreams = new Map(); // userId -> MediaStream
        this.isCallActive = false;
        this.isVideoEnabled = false;
        this.isAudioEnabled = false;
        this.callStartTime = null;
        this.callTimer = null;
        this.isScreenSharing = false;
        this.screenStream = null;
        
        // UI Elements
        this.videoContainer = null;
        this.participantsGrid = null;
        this.controlsBar = null;
        this.callDuration = null;
        
        // WebSocket for signaling
        this.signalingSocket = null;
        this.currentRoomId = null;
        this.currentUserId = null;
        this.currentUserName = null;
        
        // ICE servers configuration
        this.iceServers = [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' },
            { urls: 'stun:stun2.l.google.com:19302' }
        ];
        
        // Event callbacks
        this.onParticipantJoined = null;
        this.onParticipantLeft = null;
        this.onCallStarted = null;
        this.onCallEnded = null;
        this.onError = null;
        
        this.init();
    }
    
    init() {
        this.createUI();
        this.setupEventListeners();
    }
    
    createUI() {
        // Create main video conferencing container
        this.videoContainer = document.createElement('div');
        this.videoContainer.id = 'video-conferencing-container';
        this.videoContainer.className = 'fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex flex-col';
        
        // Create header with call info
        const header = document.createElement('div');
        header.className = 'bg-gray-900 text-white p-4 flex justify-between items-center';
        header.innerHTML = `
            <div class="flex items-center space-x-4">
                <h2 class="text-xl font-semibold">Video Conference</h2>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                    <span id="call-duration" class="text-sm">00:00</span>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <span id="participant-count" class="text-sm">1 participant</span>
                <button id="minimize-call" class="p-2 hover:bg-gray-700 rounded">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                    </svg>
                </button>
                <button id="close-call" class="p-2 hover:bg-gray-700 rounded text-red-400">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;
        
        // Create participants grid
        this.participantsGrid = document.createElement('div');
        this.participantsGrid.id = 'participants-grid';
        this.participantsGrid.className = 'flex-1 p-4 grid gap-4 auto-rows-fr';
        
        // Create controls bar
        this.controlsBar = document.createElement('div');
        this.controlsBar.className = 'bg-gray-900 p-4 flex justify-center items-center space-x-4';
        this.controlsBar.innerHTML = `
            <button id="toggle-video" class="p-3 bg-gray-700 hover:bg-gray-600 rounded-full transition-colors">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
            </button>
            <button id="toggle-audio" class="p-3 bg-gray-700 hover:bg-gray-600 rounded-full transition-colors">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                </svg>
            </button>
            <button id="share-screen" class="p-3 bg-gray-700 hover:bg-gray-600 rounded-full transition-colors">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
            </button>
            <button id="end-call" class="p-3 bg-red-600 hover:bg-red-700 rounded-full transition-colors">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 3l1.664 1.664M21 21l-1.664-1.664m0 0L3 3m16.336 16.336L3 3"></path>
                </svg>
            </button>
        `;
        
        this.videoContainer.appendChild(header);
        this.videoContainer.appendChild(this.participantsGrid);
        this.videoContainer.appendChild(this.controlsBar);
        
        // Add to document body
        document.body.appendChild(this.videoContainer);
        
        // Store references to important elements
        this.callDuration = document.getElementById('call-duration');
    }
    
    setupEventListeners() {
        // Video controls
        document.getElementById('toggle-video').addEventListener('click', () => this.toggleVideo());
        document.getElementById('toggle-audio').addEventListener('click', () => this.toggleAudio());
        document.getElementById('share-screen').addEventListener('click', () => this.toggleScreenShare());
        document.getElementById('end-call').addEventListener('click', () => this.endCall());
        document.getElementById('close-call').addEventListener('click', () => this.endCall());
        document.getElementById('minimize-call').addEventListener('click', () => this.minimizeCall());
    }
    
    // Initialize with signaling socket and room info
    initialize(signalingSocket, roomId, userId, userName) {
        this.signalingSocket = signalingSocket;
        this.currentRoomId = roomId;
        this.currentUserId = userId;
        this.currentUserName = userName;
        
        // Setup signaling message handlers
        this.setupSignalingHandlers();
    }
    
    setupSignalingHandlers() {
        // These will be called by the main app when signaling messages are received
        this.handleSignalingMessage = this.handleSignalingMessage.bind(this);
    }
    
    async startCall() {
        try {
            // Request media permissions
            this.localStream = await navigator.mediaDevices.getUserMedia({
                video: { width: 1280, height: 720 },
                audio: { echoCancellation: true, noiseSuppression: true }
            });
            
            this.isVideoEnabled = true;
            this.isAudioEnabled = true;
            this.isCallActive = true;
            this.callStartTime = Date.now();
            
            // Show video interface
            this.videoContainer.classList.remove('hidden');
            
            // Add local video
            this.addLocalVideo();
            
            // Start call timer
            this.startCallTimer();
            
            // Update grid layout
            this.updateGridLayout();
            
            // Notify other participants
            this.sendSignalingMessage({
                type: 'call-started',
                userId: this.currentUserId,
                userName: this.currentUserName
            });
            
            if (this.onCallStarted) {
                this.onCallStarted();
            }
            
        } catch (error) {
            console.error('Error starting call:', error);
            if (this.onError) {
                this.onError('Failed to start call: ' + error.message);
            }
        }
    }
    
    addLocalVideo() {
        const videoElement = this.createVideoElement(this.currentUserId, this.currentUserName, true);
        videoElement.srcObject = this.localStream;
        videoElement.muted = true; // Prevent feedback
    }
    
    createVideoElement(userId, userName, isLocal = false) {
        const participantDiv = document.createElement('div');
        participantDiv.id = `participant-${userId}`;
        participantDiv.className = 'relative bg-gray-800 rounded-lg overflow-hidden';
        
        const video = document.createElement('video');
        video.id = `video-${userId}`;
        video.className = 'w-full h-full object-cover';
        video.autoplay = true;
        video.playsInline = true;
        
        const nameLabel = document.createElement('div');
        nameLabel.className = 'absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm';
        nameLabel.textContent = isLocal ? `${userName} (You)` : userName;
        
        const statusIndicators = document.createElement('div');
        statusIndicators.className = 'absolute top-2 right-2 flex space-x-1';
        
        // Video status
        const videoStatus = document.createElement('div');
        videoStatus.id = `video-status-${userId}`;
        videoStatus.className = 'w-8 h-8 bg-green-500 rounded-full flex items-center justify-center';
        videoStatus.innerHTML = `
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
        `;
        
        // Audio status
        const audioStatus = document.createElement('div');
        audioStatus.id = `audio-status-${userId}`;
        audioStatus.className = 'w-8 h-8 bg-green-500 rounded-full flex items-center justify-center';
        audioStatus.innerHTML = `
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
            </svg>
        `;
        
        statusIndicators.appendChild(videoStatus);
        statusIndicators.appendChild(audioStatus);
        
        participantDiv.appendChild(video);
        participantDiv.appendChild(nameLabel);
        participantDiv.appendChild(statusIndicators);
        
        this.participantsGrid.appendChild(participantDiv);
        
        return video;
    }
    
    updateGridLayout() {
        const participantCount = this.participantsGrid.children.length;
        let columns, rows;
        
        if (participantCount <= 1) {
            columns = 1; rows = 1;
        } else if (participantCount <= 4) {
            columns = 2; rows = 2;
        } else if (participantCount <= 9) {
            columns = 3; rows = 3;
        } else {
            columns = 4; rows = Math.ceil(participantCount / 4);
        }
        
        this.participantsGrid.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
        this.participantsGrid.style.gridTemplateRows = `repeat(${rows}, 1fr)`;
        
        // Update participant count
        const countElement = document.getElementById('participant-count');
        if (countElement) {
            countElement.textContent = `${participantCount} participant${participantCount !== 1 ? 's' : ''}`;
        }
    }
    
    startCallTimer() {
        this.callTimer = setInterval(() => {
            if (!this.callStartTime) return;
            
            const duration = Math.floor((Date.now() - this.callStartTime) / 1000);
            const minutes = Math.floor(duration / 60);
            const seconds = duration % 60;
            
            if (this.callDuration) {
                this.callDuration.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }, 1000);
    }
    
    sendSignalingMessage(message) {
        if (this.signalingSocket && this.signalingSocket.readyState === WebSocket.OPEN) {
            this.signalingSocket.send(JSON.stringify({
                type: 'webrtc-signaling',
                roomId: this.currentRoomId,
                ...message
            }));
        }
    }

    // Handle incoming signaling messages
    async handleSignalingMessage(data) {
        try {
            switch (data.type) {
                case 'call-started':
                    if (data.userId !== this.currentUserId) {
                        await this.handleRemoteCallStarted(data);
                    }
                    break;

                case 'offer':
                    await this.handleOffer(data);
                    break;

                case 'answer':
                    await this.handleAnswer(data);
                    break;

                case 'ice-candidate':
                    await this.handleIceCandidate(data);
                    break;

                case 'media-status':
                    this.handleMediaStatusUpdate(data);
                    break;

                case 'participant-left':
                    this.handleParticipantLeft(data);
                    break;

                case 'call-ended':
                    if (data.userId !== this.currentUserId) {
                        this.handleRemoteCallEnded(data);
                    }
                    break;
            }
        } catch (error) {
            console.error('Error handling signaling message:', error);
        }
    }

    async handleRemoteCallStarted(data) {
        // Create peer connection for new participant
        await this.createPeerConnection(data.userId, data.userName);

        // Create offer if we're already in a call
        if (this.isCallActive) {
            await this.createOffer(data.userId);
        }
    }

    async createPeerConnection(userId, userName) {
        const peerConnection = new RTCPeerConnection({
            iceServers: this.iceServers
        });

        // Add local stream tracks
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => {
                peerConnection.addTrack(track, this.localStream);
            });
        }

        // Handle remote stream
        peerConnection.ontrack = (event) => {
            const [remoteStream] = event.streams;
            this.remoteStreams.set(userId, remoteStream);
            this.addRemoteVideo(userId, userName, remoteStream);
        };

        // Handle ICE candidates
        peerConnection.onicecandidate = (event) => {
            if (event.candidate) {
                this.sendSignalingMessage({
                    type: 'ice-candidate',
                    candidate: event.candidate,
                    targetUserId: userId
                });
            }
        };

        // Handle connection state changes
        peerConnection.onconnectionstatechange = () => {
            console.log(`Connection state with ${userId}:`, peerConnection.connectionState);
            if (peerConnection.connectionState === 'failed') {
                this.handleConnectionFailure(userId);
            }
        };

        this.peerConnections.set(userId, peerConnection);

        if (this.onParticipantJoined) {
            this.onParticipantJoined(userId, userName);
        }
    }

    async createOffer(userId) {
        const peerConnection = this.peerConnections.get(userId);
        if (!peerConnection) return;

        try {
            const offer = await peerConnection.createOffer();
            await peerConnection.setLocalDescription(offer);

            this.sendSignalingMessage({
                type: 'offer',
                offer: offer,
                targetUserId: userId
            });
        } catch (error) {
            console.error('Error creating offer:', error);
        }
    }

    async handleOffer(data) {
        const peerConnection = this.peerConnections.get(data.userId);
        if (!peerConnection) {
            await this.createPeerConnection(data.userId, data.userName || 'Unknown');
        }

        const pc = this.peerConnections.get(data.userId);

        try {
            await pc.setRemoteDescription(data.offer);
            const answer = await pc.createAnswer();
            await pc.setLocalDescription(answer);

            this.sendSignalingMessage({
                type: 'answer',
                answer: answer,
                targetUserId: data.userId
            });
        } catch (error) {
            console.error('Error handling offer:', error);
        }
    }

    async handleAnswer(data) {
        const peerConnection = this.peerConnections.get(data.userId);
        if (!peerConnection) return;

        try {
            await peerConnection.setRemoteDescription(data.answer);
        } catch (error) {
            console.error('Error handling answer:', error);
        }
    }

    async handleIceCandidate(data) {
        const peerConnection = this.peerConnections.get(data.userId);
        if (!peerConnection) return;

        try {
            await peerConnection.addIceCandidate(data.candidate);
        } catch (error) {
            console.error('Error adding ICE candidate:', error);
        }
    }

    addRemoteVideo(userId, userName, stream) {
        // Remove existing video element if it exists
        const existingElement = document.getElementById(`participant-${userId}`);
        if (existingElement) {
            existingElement.remove();
        }

        const videoElement = this.createVideoElement(userId, userName, false);
        videoElement.srcObject = stream;

        this.updateGridLayout();
    }

    toggleVideo() {
        if (!this.localStream) return;

        const videoTrack = this.localStream.getVideoTracks()[0];
        if (videoTrack) {
            videoTrack.enabled = !videoTrack.enabled;
            this.isVideoEnabled = videoTrack.enabled;

            this.updateLocalMediaStatus();
            this.broadcastMediaStatus();
        }
    }

    toggleAudio() {
        if (!this.localStream) return;

        const audioTrack = this.localStream.getAudioTracks()[0];
        if (audioTrack) {
            audioTrack.enabled = !audioTrack.enabled;
            this.isAudioEnabled = audioTrack.enabled;

            this.updateLocalMediaStatus();
            this.broadcastMediaStatus();
        }
    }

    updateLocalMediaStatus() {
        const videoStatus = document.getElementById(`video-status-${this.currentUserId}`);
        const audioStatus = document.getElementById(`audio-status-${this.currentUserId}`);

        if (videoStatus) {
            videoStatus.className = `w-8 h-8 ${this.isVideoEnabled ? 'bg-green-500' : 'bg-red-500'} rounded-full flex items-center justify-center`;
        }

        if (audioStatus) {
            audioStatus.className = `w-8 h-8 ${this.isAudioEnabled ? 'bg-green-500' : 'bg-red-500'} rounded-full flex items-center justify-center`;
        }

        // Update control buttons
        const videoBtn = document.getElementById('toggle-video');
        const audioBtn = document.getElementById('toggle-audio');

        if (videoBtn) {
            videoBtn.className = `p-3 ${this.isVideoEnabled ? 'bg-gray-700 hover:bg-gray-600' : 'bg-red-600 hover:bg-red-700'} rounded-full transition-colors`;
        }

        if (audioBtn) {
            audioBtn.className = `p-3 ${this.isAudioEnabled ? 'bg-gray-700 hover:bg-gray-600' : 'bg-red-600 hover:bg-red-700'} rounded-full transition-colors`;
        }
    }

    broadcastMediaStatus() {
        this.sendSignalingMessage({
            type: 'media-status',
            videoEnabled: this.isVideoEnabled,
            audioEnabled: this.isAudioEnabled
        });
    }

    handleMediaStatusUpdate(data) {
        const videoStatus = document.getElementById(`video-status-${data.userId}`);
        const audioStatus = document.getElementById(`audio-status-${data.userId}`);

        if (videoStatus) {
            videoStatus.className = `w-8 h-8 ${data.videoEnabled ? 'bg-green-500' : 'bg-red-500'} rounded-full flex items-center justify-center`;
        }

        if (audioStatus) {
            audioStatus.className = `w-8 h-8 ${data.audioEnabled ? 'bg-green-500' : 'bg-red-500'} rounded-full flex items-center justify-center`;
        }
    }

    async toggleScreenShare() {
        try {
            if (!this.isScreenSharing) {
                // Start screen sharing
                this.screenStream = await navigator.mediaDevices.getDisplayMedia({
                    video: true,
                    audio: true
                });

                // Replace video track in all peer connections
                const videoTrack = this.screenStream.getVideoTracks()[0];

                for (const [userId, peerConnection] of this.peerConnections) {
                    const sender = peerConnection.getSenders().find(s =>
                        s.track && s.track.kind === 'video'
                    );

                    if (sender) {
                        await sender.replaceTrack(videoTrack);
                    }
                }

                // Update local video
                const localVideo = document.getElementById(`video-${this.currentUserId}`);
                if (localVideo) {
                    localVideo.srcObject = this.screenStream;
                }

                // Handle screen share end
                videoTrack.onended = () => {
                    this.stopScreenShare();
                };

                this.isScreenSharing = true;

                // Update button appearance
                const shareBtn = document.getElementById('share-screen');
                if (shareBtn) {
                    shareBtn.className = 'p-3 bg-blue-600 hover:bg-blue-700 rounded-full transition-colors';
                }

            } else {
                this.stopScreenShare();
            }
        } catch (error) {
            console.error('Error toggling screen share:', error);
            if (this.onError) {
                this.onError('Failed to share screen: ' + error.message);
            }
        }
    }

    async stopScreenShare() {
        if (!this.isScreenSharing) return;

        try {
            // Stop screen stream
            if (this.screenStream) {
                this.screenStream.getTracks().forEach(track => track.stop());
                this.screenStream = null;
            }

            // Replace with camera video track
            const videoTrack = this.localStream.getVideoTracks()[0];

            for (const [userId, peerConnection] of this.peerConnections) {
                const sender = peerConnection.getSenders().find(s =>
                    s.track && s.track.kind === 'video'
                );

                if (sender && videoTrack) {
                    await sender.replaceTrack(videoTrack);
                }
            }

            // Update local video
            const localVideo = document.getElementById(`video-${this.currentUserId}`);
            if (localVideo) {
                localVideo.srcObject = this.localStream;
            }

            this.isScreenSharing = false;

            // Update button appearance
            const shareBtn = document.getElementById('share-screen');
            if (shareBtn) {
                shareBtn.className = 'p-3 bg-gray-700 hover:bg-gray-600 rounded-full transition-colors';
            }

        } catch (error) {
            console.error('Error stopping screen share:', error);
        }
    }

    handleParticipantLeft(data) {
        const participantElement = document.getElementById(`participant-${data.userId}`);
        if (participantElement) {
            participantElement.remove();
        }

        // Close peer connection
        const peerConnection = this.peerConnections.get(data.userId);
        if (peerConnection) {
            peerConnection.close();
            this.peerConnections.delete(data.userId);
        }

        // Remove remote stream
        this.remoteStreams.delete(data.userId);

        this.updateGridLayout();

        if (this.onParticipantLeft) {
            this.onParticipantLeft(data.userId);
        }
    }

    handleRemoteCallEnded(data) {
        this.handleParticipantLeft(data);
    }

    handleConnectionFailure(userId) {
        console.warn(`Connection failed with user ${userId}, attempting to reconnect...`);

        // Close existing connection
        const peerConnection = this.peerConnections.get(userId);
        if (peerConnection) {
            peerConnection.close();
            this.peerConnections.delete(userId);
        }

        // Attempt to reconnect after a delay
        setTimeout(() => {
            if (this.isCallActive) {
                this.createPeerConnection(userId, 'Reconnecting...');
            }
        }, 2000);
    }

    minimizeCall() {
        this.videoContainer.classList.add('hidden');

        // Create minimized call indicator
        const minimizedIndicator = document.createElement('div');
        minimizedIndicator.id = 'minimized-call-indicator';
        minimizedIndicator.className = 'fixed bottom-4 right-4 bg-gray-900 text-white p-3 rounded-lg shadow-lg cursor-pointer z-40';
        minimizedIndicator.innerHTML = `
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                <span class="text-sm">Call in progress</span>
                <button class="ml-2 text-gray-400 hover:text-white">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                    </svg>
                </button>
            </div>
        `;

        minimizedIndicator.addEventListener('click', () => {
            this.videoContainer.classList.remove('hidden');
            minimizedIndicator.remove();
        });

        document.body.appendChild(minimizedIndicator);
    }

    async endCall() {
        try {
            // Stop all media streams
            if (this.localStream) {
                this.localStream.getTracks().forEach(track => track.stop());
                this.localStream = null;
            }

            if (this.screenStream) {
                this.screenStream.getTracks().forEach(track => track.stop());
                this.screenStream = null;
            }

            // Close all peer connections
            for (const [userId, peerConnection] of this.peerConnections) {
                peerConnection.close();
            }
            this.peerConnections.clear();
            this.remoteStreams.clear();

            // Stop call timer
            if (this.callTimer) {
                clearInterval(this.callTimer);
                this.callTimer = null;
            }

            // Reset state
            this.isCallActive = false;
            this.isVideoEnabled = false;
            this.isAudioEnabled = false;
            this.isScreenSharing = false;
            this.callStartTime = null;

            // Hide video interface
            this.videoContainer.classList.add('hidden');

            // Clear participants grid
            this.participantsGrid.innerHTML = '';

            // Remove minimized indicator if exists
            const minimizedIndicator = document.getElementById('minimized-call-indicator');
            if (minimizedIndicator) {
                minimizedIndicator.remove();
            }

            // Notify other participants
            this.sendSignalingMessage({
                type: 'call-ended',
                userId: this.currentUserId
            });

            if (this.onCallEnded) {
                this.onCallEnded();
            }

        } catch (error) {
            console.error('Error ending call:', error);
        }
    }

    // Utility method to check if call is active
    isInCall() {
        return this.isCallActive;
    }

    // Get current participant count
    getParticipantCount() {
        return this.peerConnections.size + (this.isCallActive ? 1 : 0);
    }

    // Get list of participants
    getParticipants() {
        const participants = [];
        if (this.isCallActive) {
            participants.push({
                userId: this.currentUserId,
                userName: this.currentUserName,
                isLocal: true
            });
        }

        for (const userId of this.peerConnections.keys()) {
            participants.push({
                userId: userId,
                userName: 'Remote User', // Could be enhanced to store names
                isLocal: false
            });
        }

        return participants;
    }
}

// Export for use in main application
window.VideoConferencing = VideoConferencing;
